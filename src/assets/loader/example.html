<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Empty Index File</title>
  <style>
    /* Container for zoomed image */
    #zoomedImageContainer {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.8);
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    /* Wrapper for image and text */
    .imageWrapper {
      position: relative;
    }

    #zoomedImage {
      max-width: 1380px;
      max-height: 900px;
      width: 100%;
      height: auto;
      display: block;
    }

    /* Alt text inside the image */
    #altText {
      position: absolute;
      top: 10px;
      left: 10px;
      background-color: rgba(0, 0, 0, 0.5); /* Light black background */
      color: #fff;
      padding: 5px 10px;
      border-radius: 10px;
      font-size: 14px;
      max-width: 80%;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .closeButton {
      position: absolute;
      top: 10px;
      right: 10px;
      background-color: rgba(0, 0, 0, 0.5);
      color: white;
      border: none;
      font-size: 24px;
      padding: 1px 8px;
      cursor: pointer;
      border-radius: 50%;
    }

    .closeButton:hover {
      background-color: rgba(0, 0, 0, 0.8);
    }
  </style>
  <script
    src="http://localhost:52187/assets/loader/loader.js"
    data-link="https://embed.dxconnect.lifesup.ai/"
    data-id="accfb9f5-8475-427a-a4b7-3f30f911544b"
    data-bubble-background-color="#7241FF"
    data-bubble-color="#FFFFFF"
    data-bubble-content="https://hadong.lifesup.ai/favicon.ico"
    data-bubble-padding="10px"
    data-bubble-border-radius="50%"
    data-widget-background-color="#123456"
    data-widget-color="#000000"
    data-content-img="https://www.dxsuite.io/svgs/homepage/logo.svg"
    data-content-text="Hello"
    data-chat-text-size="15px"
    data-chat-line-height=""
    data-assistant-background="#7241FF"
    data-assistant-color="#fff"
    data-assistant-avatar=""
    data-user-background="#2A85FF"
    data-user-color="#FFFFFF"
    data-powered-by="DxGPT"
    data-powered-link=""
  ></script>
</head>
<body>

<!-- Zoomed Image Container -->
<div id="zoomedImageContainer" onclick="closeZoom()">
  <!-- Wrapper around image and alt text -->
  <div class="imageWrapper">
    <img id="zoomedImage" src="" alt="" />
    <button class="closeButton" onclick="closeZoom()">
      <svg width="16px" height="16px" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
        <path d="M20.7457 3.32851C20.3552 2.93798 19.722 2.93798 19.3315 3.32851L12.0371 10.6229L4.74275 3.32851C4.35223 2.93798 3.71906 2.93798 3.32854 3.32851C2.93801 3.71903 2.93801 4.3522 3.32854 4.74272L10.6229 12.0371L3.32856 19.3314C2.93803 19.722 2.93803 20.3551 3.32856 20.7457C3.71908 21.1362 4.35225 21.1362 4.74277 20.7457L12.0371 13.4513L19.3315 20.7457C19.722 21.1362 20.3552 21.1362 20.7457 20.7457C21.1362 20.3551 21.1362 19.722 20.7457 19.3315L13.4513 12.0371L20.7457 4.74272C21.1362 4.3522 21.1362 3.71903 20.7457 3.32851Z" fill="#FFFFFF"/>
      </svg>
    </button>
  </div>
</div>

<script>
  // Ensure DOM is ready before setting up the message listener
  document.addEventListener('DOMContentLoaded', function () {
    window.addEventListener('message', (event) => {
      console.log('Received message:', event.data); // Debugging message

      // Check if the action is zoomImage
      if (event.data.action === 'zoomImage') {
        const zoomedImageContainer = document.getElementById('zoomedImageContainer');
        const zoomedImage = document.getElementById('zoomedImage');

        // Assign image source
        zoomedImage.src = event.data.src;

        // Remove any existing altText div
        const existingAltText = document.getElementById('altText');
        if (existingAltText) {
          existingAltText.remove();
        }

        // Conditionally add altText div if event.data.alt exists
        if (event.data.alt) {
          const altTextDiv = document.createElement('div');
          altTextDiv.id = 'altText';
          altTextDiv.textContent = event.data.alt;
          document.querySelector('.imageWrapper').appendChild(altTextDiv);
        }

        // Display zoomed image container
        zoomedImageContainer.style.display = 'flex';
      } else {
        console.warn('Unexpected message action:', event.data.action);
      }
    });
  });

  // Function to close zoomed image on click
  function closeZoom() {
    document.getElementById('zoomedImageContainer').style.display = 'none';
  }

  // Optional window.onload function to demonstrate post-load actions
  window.onload = function () {
    // Only load userInfo from localStorage if it exists
    const savedUserInfo = localStorage.getItem('userInfo');
    if (savedUserInfo && typeof window.setUser === 'function') {
      try {
        const userData = JSON.parse(savedUserInfo);
        window.setUser(userData);
        console.log('Loaded user from localStorage:', userData);
      } catch (error) {
        console.warn('Failed to parse userInfo from localStorage:', error);
      }
    }
    // No default user - only use saved data if available
  };

  function clearDemoUser() {
    if (typeof window.clearUser === 'function') {
      window.clearUser();
      alert('User info cleared!');
    }
  }
</script>

<!-- Demo buttons -->
<div style="position: fixed; top: 10px; left: 10px; z-index: 1001;">
  <button onclick="clearDemoUser()" style="margin: 5px; padding: 10px; background-color: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer;">
    Clear User Info
  </button>
</div>

</body>
</html>
