(() => {
  function initWidgetWithShadowDom() {
    // Tạo container ngoài cùng và shadow root
    const shadowContainer = document.createElement('div');
    shadowContainer.id = 'dx-shadow-widget-root';
    shadowContainer.style.all = 'initial'; // hạn chế CSS ảnh hưởng từ trang ngoài
    document.body.appendChild(shadowContainer);

    const fontLink = document.createElement("link");
    fontLink.rel = "stylesheet";
    fontLink.href =
      "https://embed.dxconnect.lifesup.ai/assets/loader/font.css";
    document.head.appendChild(fontLink);

    const shadowRoot = shadowContainer.attachShadow({ mode: 'open' });

    // CSS riêng của widget đưa vào shadowRoot (cô lập style)
    const cssLink = document.createElement("link");
    cssLink.rel = "stylesheet";
    cssLink.href =
      "https://embed.dxconnect.lifesup.ai/assets/loader/loader.css";
    shadowRoot.appendChild(cssLink);

    const script = document.currentScript
      || document.querySelector('script[data-link]')
      || document.scripts[document.scripts.length - 1];
    const loadWidget = () => {
      const link = script.dataset.link;
      const id = script.dataset.id;

      // TEMPORARILY COMMENTED OUT - Expand/Compress functionality
      // const svgCompress = `<svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      // <path d="M9 4V9H4M15 4V9H20M4 15H9V20M15 20V15H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      // </svg>`;
      // const svgExpand = `<svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      // <path d="M4 9V5.6C4 5.03995 4 4.75992 4.10899 4.54601C4.20487 4.35785 4.35785 4.20487 4.54601 4.109C4.75992 4 5.03995 4 5.6 4L9 4M4 15V18.4C4 18.9601 4 19.2401 4.10899 19.454C4.20487 19.6422 4.35785 19.7951 4.54601 19.891C4.75992 20 5.03995 20 5.6 20L9 20M15 4H18.4C18.9601 4 19.2401 4 19.454 4.10899C19.6422 4.20487 19.7951 4.35785 19.891 4.54601C20 4.75992 20 5.03995 20 5.6V9M20 15V18.4C20 18.9601 20 19.2401 19.891 19.454C19.7951 19.6422 19.6422 19.7951 19.454 19.891C19.2401 20 18.9601 20 18.4 20H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
      // </svg>`;
      const svgRefresh = `<svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12 21C16.9706 21 21 16.9706 21 12C21 9.69494 20.1334 7.59227 18.7083 6L16 3M12 3C7.02944 3 3 7.02944 3 12C3 14.3051 3.86656 16.4077 5.29168 18L8 21M21 3H16M16 3V8M3 21H8M8 21V16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`;
      const svgClose = `<svg width="20px" height="20px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M20.7457 3.32851C20.3552 2.93798 19.722 2.93798 19.3315 3.32851L12.0371 10.6229L4.74275 3.32851C4.35223 2.93798 3.71906 2.93798 3.32854 3.32851C2.93801 3.71903 2.93801 4.3522 3.32854 4.74272L10.6229 12.0371L3.32856 19.3314C2.93803 19.722 2.93803 20.3551 3.32856 20.7457C3.71908 21.1362 4.35225 21.1362 4.74277 20.7457L12.0371 13.4513L19.3315 20.7457C19.722 21.1362 20.3552 21.1362 20.7457 20.7457C21.1362 20.3551 21.1362 19.722 20.7457 19.3315L13.4513 12.0371L20.7457 4.74272C21.1362 4.3522 21.1362 3.71903 20.7457 3.32851Z" fill="currentColor"/>
</svg>`;


      const widgetBackgroundColor = script.dataset.widgetBackgroundColor || "#FFFFFF";
      const widgetHeaderBackgroundColor = script.dataset.widgetHeaderBackgroundColor || "#010314";
      let widgetWidth = (() => {
        const defaultWidth = script.dataset.widgetWidth || "500px";
        return window.innerWidth < 768 ? "100%" : defaultWidth;
      })();

      const widgetColor = script.dataset.widgetColor || "#333333";
      const widget = document.createElement("div");
      widget.classList.add("animated-div", "hide-embed-widget", "position-mini");
      const widgetStyle = widget.style;
      widgetStyle.boxSizing = "border-box";
      widgetStyle.position = "fixed";
      widgetStyle.overflow = "hidden";
      widgetStyle.backgroundColor = widgetBackgroundColor;
      widgetStyle.width = widgetWidth;
      widgetStyle.borderRadius = "15px";
      widgetStyle.paddingBottom= "12px"
      widgetStyle.boxShadow = "0 0 10px rgba(0, 0, 0, 0.4)";
      widgetStyle.flexDirection= "column"
      widget.style.display = "none";

      const widgetIcon = document.createElement("div");
      widgetIconStyle = widgetIcon.style;
      widgetIconStyle.display = "flex";
      widgetIconStyle.alignItems = "center";
      widgetIconStyle.justifyContent = "space-between";
      widgetIconStyle.padding = "0.8rem 1rem";
      widgetIconStyle.backgroundColor = widgetHeaderBackgroundColor;
      widgetIconStyle.borderTopLeftRadius = "15px";
      widgetIconStyle.borderTopRightRadius = "15px";

      widget.appendChild(widgetIcon);

      // TEMPORARILY COMMENTED OUT - leftIcon container (was for expand/compress button)
      // const leftIcon = document.createElement("div");
      const rightIcon = document.createElement("div");

      // TEMPORARILY COMMENTED OUT - Expand/Compress functionality
      // Nút phóng to/thu nhỏ
      // const fullscreenButton = document.createElement("button");
      // fullscreenButton.innerHTML = svgExpand;
      // fullscreenButton.style.padding = "0px";
      // fullscreenButton.style.backgroundColor = "transparent";
      // fullscreenButton.style.border = "none";
      // fullscreenButton.style.cursor = "pointer";
      // fullscreenButton.style.color = widgetColor;
      // fullscreenButton.color = widgetColor;
      // fullscreenButton.addEventListener("click", () => {
      //   if (widget.classList.contains("position-fullSize")) {
      //     compressWidget();
      //   } else {
      //     expandWidget();
      //   }
      // });
      // leftIcon.appendChild(fullscreenButton);

      // Nút refresh
      const refreshButton = document.createElement("button");

      refreshButton.innerHTML = svgRefresh;
      refreshButton.style.padding = "0px 6px 0px";
      refreshButton.style.backgroundColor = "transparent";
      refreshButton.style.border = "none";
      refreshButton.style.cursor = "pointer";
      refreshButton.style.color = widgetColor;
      refreshButton.addEventListener("click", () => {
        iframe.contentWindow.postMessage({ action: "refresh" }, link);
      });
      rightIcon.appendChild(refreshButton);

      // Nút close
      const closeButton = document.createElement("button");
      closeButton.innerHTML = svgClose;
      closeButton.style.padding = "0px";
      closeButton.style.backgroundColor = "transparent";
      closeButton.style.border = "none";
      closeButton.style.cursor = "pointer";
      closeButton.style.color = widgetColor;
      closeButton.onclick = function () {
        toggleChatWidget(widget);
      };
      rightIcon.appendChild(closeButton);

      // Powered by
      const brand = script.dataset.poweredBy || "DxConnect";
      const linkBrand = script.dataset.poweredLink;
      const poweredByText = document.createElement(linkBrand ? "a" : "div");
      poweredByText.classList.add("text-type");
      poweredByText.innerText = "Powered by " + brand;
      if (linkBrand) {
        poweredByText.href = linkBrand;
        poweredByText.target = "_blank";
      }
      widget.appendChild(poweredByText);
      const poweredByTextStyle = poweredByText.style;
      poweredByTextStyle.position = "absolute";
      poweredByTextStyle.bottom = "10px";
      poweredByTextStyle.left = "50%";
      poweredByTextStyle.transform = "translateX(-50%)";
      poweredByTextStyle.color = "#888e91";
      poweredByTextStyle.fontSize= "11px"
      poweredByTextStyle.textDecoration = "none";

      // Content headline và logo
      const contentText = script.dataset.contentText;
      const contentImg = script.dataset.contentImg;

      const flexContainer = document.createElement("div");
      flexContainer.style.display = "flex";
      flexContainer.style.gap = "12px";
      flexContainer.style.justifyContent = "center";
      flexContainer.style.alignItems = "center";

      if (contentImg) {
        const logoContainer = document.createElement("div");
        const logoImage = document.createElement("img");
        logoImage.src = contentImg;
        logoImage.alt = "logo";
        logoImage.style.width = "auto";
        logoImage.style.maxHeight = "40px";
        logoContainer.appendChild(logoImage);
        flexContainer.appendChild(logoContainer);
      }

      if (contentText) {
        // Create a vertical container for title and status
        const textAndStatusContainer = document.createElement("div");
        textAndStatusContainer.style.display = "flex";
        textAndStatusContainer.style.flexDirection = "column";
        textAndStatusContainer.style.alignItems = "flex-start"; // Căn trái thay vì center
        textAndStatusContainer.style.gap = "4px";

        // Title text
        const headlineContainer = document.createElement("div");
        headlineContainer.style.display = "flex";
        headlineContainer.style.fontSize = "1.25rem";
        headlineContainer.style.color = widgetColor;
        headlineContainer.style.textAlign = "left"; // Căn trái thay vì center
        headlineContainer.style.whiteSpace = "nowrap";
        headlineContainer.textContent = contentText;
        headlineContainer.classList.add("text-type");

        // Status indicator - Online with green dot (positioned below title)
        const statusContainer = document.createElement("div");
        statusContainer.style.display = "flex";
        statusContainer.style.alignItems = "center";
        statusContainer.style.gap = "6px";

        // Green dot indicator
        const statusDot = document.createElement("div");
        statusDot.style.width = "8px";
        statusDot.style.height = "8px";
        statusDot.style.backgroundColor = "#22C55E"; // Green color
        statusDot.style.borderRadius = "50%";
        statusDot.style.flexShrink = "0"; // Prevent dot from shrinking

        // Status text
        const statusText = document.createElement("span");
        statusText.textContent = "Online";
        statusText.style.fontSize = "0.75rem"; // Smaller than title
        statusText.style.color = widgetColor;
        statusText.style.fontWeight = "400";
        statusText.style.whiteSpace = "nowrap";
        statusText.classList.add("text-type");

        statusContainer.appendChild(statusDot);
        statusContainer.appendChild(statusText);

        // Add title and status to vertical container
        textAndStatusContainer.appendChild(headlineContainer);
        textAndStatusContainer.appendChild(statusContainer);

        flexContainer.appendChild(textAndStatusContainer);
      } else {
        // If no contentText, show only status indicator
        const statusContainer = document.createElement("div");
        statusContainer.style.display = "flex";
        statusContainer.style.alignItems = "center";
        statusContainer.style.gap = "6px";

        // Green dot indicator
        const statusDot = document.createElement("div");
        statusDot.style.width = "8px";
        statusDot.style.height = "8px";
        statusDot.style.backgroundColor = "#22C55E"; // Green color
        statusDot.style.borderRadius = "50%";
        statusDot.style.flexShrink = "0"; // Prevent dot from shrinking

        // Status text
        const statusText = document.createElement("span");
        statusText.textContent = "Online";
        statusText.style.fontSize = "0.875rem";
        statusText.style.color = widgetColor;
        statusText.style.fontWeight = "400";
        statusText.style.whiteSpace = "nowrap";
        statusText.classList.add("text-type");

        statusContainer.appendChild(statusDot);
        statusContainer.appendChild(statusText);
        flexContainer.appendChild(statusContainer);
      }

      // TEMPORARILY COMMENTED OUT - leftIcon container (was for expand/compress button)
      // widgetIcon.appendChild(leftIcon);
      widgetIcon.appendChild(flexContainer);
      widgetIcon.appendChild(rightIcon);

      // Container cho iframe
      const containerDiv = document.createElement("div");
      const containerStyle = containerDiv.style;
      containerStyle.width = "100%";
      containerStyle.height = "100%";

      const iframe = document.createElement("iframe");
      const iframeStyle = iframe.style;
      iframeStyle.boxSizing = "borderBox";
      iframeStyle.width = "100%";
      iframeStyle.height = "100%";
      iframeStyle.border = 0;
      iframeStyle.margin = 0;
      iframeStyle.borderTop = "1px solid #E1E1E1";
      iframe.id = "iframeChat";
      containerDiv.appendChild(iframe);

      widget.appendChild(containerDiv);
      iframe.src = link;

      // Chat bubble
      const bubbleBackgroundColor = script.dataset.bubbleBackgroundColor || "#007bff";
      const bubbleColor = script.dataset.bubbleColor || "white";
      const bubblePadding = script.dataset.bubblePadding || "10px";
      const bubbleBorderRadius = script.dataset.bubbleBorderRadius || "50%";
      const bubbleContent = script.dataset.bubbleContent;
      const isURL = /^(http|https):\/\/[^ "]+$/.test(bubbleContent);

      const chatBubble = document.createElement("div");
      widget.classList.add("buttonElement");
      chatBubble.id = "chat-bubble";

      if (isURL) {
        chatBubble.style.backgroundImage = `url(${bubbleContent})`;
        chatBubble.style.backgroundSize = "contain";
        chatBubble.style.backgroundRepeat = "no-repeat";
        chatBubble.style.backgroundPosition = "center";
        chatBubble.style.width = "60px";
        chatBubble.style.height = "60px";
      } else {
        chatBubble.textContent = bubbleContent;
        chatBubble.style.backgroundColor = bubbleBackgroundColor;
      }

      chatBubble.classList.add("bubble-size");
      const chatBubbleStyle = chatBubble.style;
      chatBubbleStyle.color = bubbleColor;
      chatBubbleStyle.padding = bubblePadding;
      chatBubbleStyle.borderRadius = bubbleBorderRadius;
      chatBubbleStyle.cursor = "pointer";
      chatBubbleStyle.textAlign = "center";
      chatBubbleStyle.position = "fixed";
      chatBubbleStyle.right = "20px";
      chatBubbleStyle.bottom = "20px";
      chatBubbleStyle.display = "flex";
      chatBubbleStyle.justifyContent = "center";
      chatBubbleStyle.alignItems = "center";
      chatBubbleStyle.zIndex = "998";

      chatBubble.onclick = function () {
        toggleChatWidget(widget);
      };

      // Chèn widget và bubble vào shadowRoot
      shadowRoot.appendChild(widget);
      shadowRoot.appendChild(chatBubble);

      // Các function user, postMessage... giữ nguyên
      window.setUser = (userData) => {
        const string = JSON.stringify(userData)
        localStorage.setItem('userInfo', string);
        setTimeout(() => {
          iframe.contentWindow.postMessage({ type: "addUser", userData: userData }, link);
        }, 1000);
      };

      window.clearUser = () => {
        localStorage.removeItem('userInfo');
        setTimeout(() => {
          iframe.contentWindow.postMessage({ type: "clearUser" }, link);
        }, 100);
      };

      const loadUserFromStorage = () => {
        const savedUserInfo = localStorage.getItem('userInfo');
        if (savedUserInfo) {
          try {
            const userData = JSON.parse(savedUserInfo);
            setTimeout(() => {
              iframe.contentWindow.postMessage({ type: "addUser", userData: userData }, link);
            }, 1000);
          } catch (error) {
            console.warn('Failed to parse userInfo from localStorage:', error);
          }
        }
      };

      const chatTextSize = script.dataset.chatTextSize || "14px";
      const chatLineHeight = script.dataset.chatLineHeight || "unset";
      const userColor = script.dataset.userColor || "white";
      const userBackground = script.dataset.userBackground || "#7241ff";
      const assistantColor = script.dataset.assistantColor || "black";
      const assistantAvatar = script.dataset.assistantAvatar || null;
      const assistantBackground = script.dataset.assistantBackground || "#F3F4F6";

      iframe.addEventListener("load", () => {
        iframe.contentWindow.postMessage(
          {
            type: "attr",
            attr: {
              chatTextSize: chatTextSize,
              chatLineHeight: chatLineHeight,
              userColor: userColor,
              userBackground: userBackground,
              assistantColor: assistantColor,
              assistantAvatar: assistantAvatar,
              assistantBackground: assistantBackground,
              bubbleColor: bubbleBackgroundColor,
            },
          },
          link,
        );
        const match =  window.location.hostname.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/i);
        const domain = match ? match[1] : null;
        iframe.contentWindow.postMessage({ type: "domain", domain }, link);
        iframe.contentWindow.postMessage({ type: "ai_id", id }, link);

        loadUserFromStorage();
      });

      function toggleChatWidget(widget) {
        if (widget.classList.contains("hide-embed-widget")) {
          // Ensure widget is in mini mode when opening (replaces compressWidget() call)
          widget.classList.remove("position-fullSize");
          widget.classList.add("position-mini");
          setWidgetSize();

          widget.classList.remove("hide-embed-widget");
          widget.classList.add("show-embed-widget");
          widget.style.display = "flex";
        } else {
          widget.classList.remove("show-embed-widget");
          widget.classList.add("hide-embed-widget");
          setTimeout(function () {
            widget.style.display = "none";
          }, 200);
        }
      }

      const setWidgetSize = () => {
        if (widget.classList.contains("position-mini")) {
          widgetStyle.width = widgetWidth;
        } else if (widget.classList.contains("position-fullSize")) {
          widgetStyle.width = "100%";
        }
      };

      // TEMPORARILY COMMENTED OUT - Expand/Compress functionality
      // function compressWidget() {
      //   widget.classList.remove("position-fullSize");
      //   widget.classList.add("position-mini");
      //   fullscreenButton.innerHTML = svgExpand;
      //   setWidgetSize();
      // }

      // function expandWidget() {
      //   widget.classList.remove("position-mini");
      //   widget.classList.add("position-fullSize");
      //   setWidgetSize();
      //   iframeStyle.borderRadius = "0px";
      //   fullscreenButton.innerHTML = svgCompress;
      // }
    };

    loadWidget();
  }

  // Đảm bảo DOM đã sẵn sàng
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initWidgetWithShadowDom);
  } else {
    initWidgetWithShadowDom();
  }
})();
