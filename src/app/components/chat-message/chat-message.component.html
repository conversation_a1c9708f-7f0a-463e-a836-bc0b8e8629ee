<div class="w-full h-full relative m-auto" style="max-width: 1200px">
  <div class="chat-frame px-4 lg:px-16 pt-4 mb-0 w-full mt-0 md:m-auto">
    <div #chatContainer class="chat-content-container flex-1 md:px-4 py-4 overflow-y-auto overflow-x-hidden"
    [style.fontSize]="chatTextSize" [style.lineHeight]="chatLineHeight">
      <!-- chat message -->
      <ng-scrollbar
        class="scroll-custom"
        #scrollRef
        track="vertical"
        appearance="standard"
        visibility="hover"
      >
        <div *ngIf="listChatMessage.length != 0" class="w-full">
          <ng-container *ngFor="let msg of listChatMessage;let i = index">
            <div class="mb-3">
              <div *ngIf="(msg.role == 'assistant' || msg.role == 'human_operator') && msg.content" class=" w-full md:w-4/5">
                <div class="flex gap-4" [style.color]="assistantColor">
                  <img *ngIf="assistantAvatar" [src]="assistantAvatar" alt="Logo" class="rounded-full w-10 h-10">
                  <div>
                    <div [style.background-color]="assistantBackground" class="px-3 py-2 rounded-xl mb-1 relative w-full 2xl:max-w-[80vw]">
                      <div class="relative">
                        <div
                          *ngIf="msg.typeMessage == 'text'"
                          class="msg-bubble !block"
                        >
                          <div
                            *ngIf="msg.typeMessage == 'text'"
                            [innerHTML]="msg.content | safeHtml"
                            matTooltipTouchGestures="off"
                          >
                          </div>
                          <ng-container
                            *ngIf="msg.showSource && msg.listTextSources && msg.listTextSources.length > 0">
                            <br/>
                            <div class="grid grid-cols-3">
                              <div
                                class="mb-2 p-3 border border-gray-200 bg-gray-50 rounded-2xl grid gap-4"
                                [ngClass]="{
                                'col-span-1 grid-cols-1': msg.listTextSources.length === 1,
                                'col-span-2 grid-cols-2': msg.listTextSources.length === 2,
                                'col-span-3 grid-cols-3': msg.listTextSources.length >= 3
                              }"
                              >
                                <ng-container *ngFor="let source of msg.listTextSources">
                                  <a
                                    class="col-span-1 flex items-center justify-between space-x-1 px-2 py-1 bg-[#d4c6ff] text-black rounded-xl cursor-pointer hover:bg-[#f0ecff] hover:text-light-primary hover:underline"
                                    [matTooltip]="source ? source?.source : ''"
                                    [href]="source?.source"
                                    target="_blank"
                                    matTooltipPosition="below"
                                    matTooltipClass="cus-tooltip"
                                  >
                                    <div class="flex-grow truncate">{{ source?.title }}</div>
                                    <div class="flex items-center justify-center">
                                      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" width="1em" height="1em"
                                           fill="black" class="text-right">
                                        <path
                                          d="M352 224H305.5c-45 0-81.5 36.5-81.5 81.5c0 22.3 10.3 34.3 19.2 40.5c6.8 4.7 12.8 12 12.8 20.3c0 9.8-8 17.8-17.8 17.8h-2.5c-2.4 0-4.8-.4-7.1-1.4C210.8 374.8 128 333.4 128 240c0-79.5 64.5-144 144-144h80V34.7C352 15.5 367.5 0 386.7 0c8.6 0 16.8 3.2 23.2 8.9L548.1 133.3c7.6 6.8 11.9 16.5 11.9 26.7s-4.3 19.9-11.9 26.7l-139 125.1c-5.9 5.3-13.5 8.2-21.4 8.2H384c-17.7 0-32-14.3-32-32V224zM80 96c-8.8 0-16 7.2-16 16V432c0 8.8 7.2 16 16 16H400c8.8 0 16-7.2 16-16V384c0-17.7 14.3-32 32-32s32 14.3 32 32v48c0 44.2-35.8 80-80 80H80c-44.2 0-80-35.8-80-80V112C0 67.8 35.8 32 80 32h48c17.7 0 32 14.3 32 32s-14.3 32-32 32H80z"/>
                                      </svg>
                                    </div>
                                  </a>
                                </ng-container>
                              </div>
                            </div>
                          </ng-container>
                        </div>
                        <app-image-gallery
                          style="--max-width: 364px; --img-width: 110px; --img-height: 110px; --gap: 10px;  --label-space: 10px"
                          *ngIf="msg.typeMessage == 'gallery'" [imageSrcs]="msg.listImages"></app-image-gallery>
                        <app-image-grid style="--col: 2" *ngIf="msg.typeMessage == 'grid'"
                                        [imageSrcs]="msg.listImages"></app-image-grid>
                      </div>
                    </div>
                    <p class="text-[11px] text-[#384248]">{{msg.createAt | dateFormatter}}</p>
                  </div>
                </div>
              </div>
              <div *ngIf="msg.role == 'user'"
                   class="flex w-full md:max-w-[60vw] ml-auto items-center flex-row-reverse gap-8 ">
                <ng-container *ngIf="msg.typeMessage !== 'complex'">
                  <div [style.background-color]="userBackground"
                       [style.color]="userColor"
                       class=" px-3 py-2 rounded-xl mb-1 relative">
                    <span class="break-words whitespace-pre-wrap" [innerHTML]="msg.content | safeHtml"></span>
                    <div [style.background-color]="userBackground"
                         class="absolute right-0 top-1/2 transform translate-x-1/2 rotate-45 w-2 h-2"></div>
                  </div>
                  <p class="text-[11px] text-[#384248]" [ngClass]="{'flex-row-reverse flex': msg.role == 'user'}">{{msg.createAt | dateFormatter}}</p>
                </ng-container>

                <ng-container *ngIf="msg.typeMessage == 'complex' && msg.role == 'user' && msg.content">
                  <app-complex-message
                    [type]="msg.content.complex_type"
                    [content]="msg.content"
                    (onActionClick)="sendMessage('', $event)"
                  ></app-complex-message>
                </ng-container>

              </div>
            </div>
          </ng-container>
          <div *ngIf="isTyping" class="flex items-center mb-4 gap-4">
            <img *ngIf="assistantAvatar" [src]="assistantAvatar" alt="Logo" class="rounded-full w-10 h-10">
<!--            <div class="flex-none  hidden md:flex flex-col items-center space-y-1 mr-4">-->
<!--              <svg class="rounded-full w-10 h-10" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--                <path-->
<!--                  d="M9.9329 18.5411H13.5239V27.718C12.0385 26.8215 10.8034 25.5647 9.9329 24.064V18.5411ZM41.7458 37.5039C41.9087 37.6689 42 37.8915 42 38.1233C42 38.3552 41.9087 38.5778 41.7458 38.7428L38.7428 41.7458C38.5778 41.9087 38.3552 42 38.1233 42C37.8915 42 37.6689 41.9087 37.5039 41.7458L30.0699 34.3119C30.0658 34.3079 30.0617 34.3038 30.0577 34.2996C30.0227 34.2646 30.0087 34.2209 29.9824 34.1841C26.7643 36.5336 22.8826 37.7991 18.8981 37.7979C8.46116 37.7979 0 29.3367 0 18.8981C0 8.45941 8.46116 0 18.8998 0C29.3384 0 37.7979 8.46116 37.7979 18.8998C37.7979 23.0455 36.4469 26.8675 34.1824 29.9824C34.2261 30.0122 34.2734 30.0314 34.3119 30.0699L41.7458 37.5039ZM32.9469 18.8981C32.9424 16.1259 32.1165 13.4172 30.5736 11.1141C29.0307 8.81105 26.8399 7.01682 24.2779 5.95804C21.7159 4.89926 18.8976 4.62341 16.1789 5.16534C13.4602 5.70726 10.9631 7.04265 9.00287 9.00287C7.04265 10.9631 5.70726 13.4602 5.16534 16.1789C4.62341 18.8976 4.89926 21.7159 5.95804 24.2779C7.01682 26.8399 8.81105 29.0307 11.1141 30.5736C13.4172 32.1165 16.1259 32.9424 18.8981 32.9469H18.9401C20.7822 32.9441 22.6058 32.5786 24.3067 31.8711C26.0076 31.1636 27.5524 30.128 28.8531 28.8234C30.1538 27.5188 31.1847 25.9709 31.8871 24.2679C32.5896 22.5649 32.9497 20.7402 32.9469 18.8981ZM24.442 27.613C25.9352 26.6604 27.1633 25.3456 28.012 23.791V20.704H24.442V27.613ZM16.5671 29.2719C17.3406 29.3717 18.1193 29.4119 18.8981 29.3979C19.598 29.4417 20.298 29.4417 20.998 29.3979V13.4609H16.5688V29.2719H16.5671Z"-->
<!--                  fill="url(#paint0_linear_32_63)"/>-->
<!--                <defs>-->
<!--                  <linearGradient id="paint0_linear_32_63" x1="21.2079" y1="41.876" x2="19.5639" y2="-0.635959"-->
<!--                                  gradientUnits="userSpaceOnUse">-->
<!--                    <stop stop-color="#5599FF"/>-->
<!--                    <stop offset="0.836794" stop-color="#7241FF"/>-->
<!--                    <stop offset="1" stop-color="#8E66FF"/>-->
<!--                  </linearGradient>-->
<!--                </defs>-->
<!--              </svg>-->
<!--              <a href="#" class="block text-xs hover:underline">QueryGPT</a>-->
<!--            </div>-->
            <div class="typing-animation bg-white p-2 rounded-lg mb-2 relative">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>
        </div>
      </ng-scrollbar>
    </div>
  </div>

  <div class="chat-input-container border-t pt-2 container mt-4 w-full lg:w-3/4 flex justify-center m-auto">
    <div class="chat-input w-full mx-4 flex items-end gap-3">
      <div class="flex-1 bg-white rounded-2xl px-4 py-3">
        <textarea id="chat" rows="1"
                  #inputTextArea
                  class="block w-full text-sm text-gray-900 bg-transparent border-0 outline-none resize-none max-h-[200px]"
                  [placeholder]="inputPlaceholder" (keydown)="onKeyDown($event)" [(ngModel)]="messageInput"
                  (ngModelChange)="updateTextAreaHeight()"></textarea>
      </div>
      <button type="submit" (click)="sendMessage(messageInput)"
              class="w-10 h-10 rounded-full bg-primary hover:bg-primaryLight text-white flex items-center justify-center transition-colors">
        <i class="fas fa-paper-plane text-sm"></i>
      </button>
    </div>
  </div>
</div>

